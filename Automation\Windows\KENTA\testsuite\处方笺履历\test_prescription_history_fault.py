# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-07
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   test_prescription_history_fault.py
@Software   :   PyCharm
"""

import allure
from neox_test_common import logger
from neox_test_scenarios.yakushi.pages import PrescriptionHistoryPage


@allure.epic("薬師丸賢太")
@allure.suite("处方笺履历")
@allure.sub_suite("提交不具合报告")
@allure.feature("处方笺履历")
@allure.story("提交不具合报告")
@allure.title("测试用例：提交不具合报告")
def test_prescription_history_submit_fault_report(config):
    """
    TestCase: 处方笺履历 - 提交不具合报告

    测试在处方笺履历页面提交故障报告的功能
    """
    prescription_page = PrescriptionHistoryPage(config)

    with allure.step("提交不具合报告"):
        logger.info("< Test :: 提交不具合报告 >")

        # 验证处方笺履历页面已加载
        assert prescription_page.verify_prescription_history_page_loaded(), (
            "处方笺履历页面未正确加载"
        )

        # 提交故障报告
        fault_description = "测试故障报告：处方笺显示异常"
        assert prescription_page.submit_fault_report(fault_description), (
            "提交故障报告失败"
        )

        logger.info("故障报告提交测试完成")
