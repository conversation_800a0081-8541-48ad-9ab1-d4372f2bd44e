# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-07
<AUTHOR>   <PERSON>no<PERSON>u
@Email      :   <EMAIL>
@File       :   test_nsips_comparison_src_cleanup.py
@Software   :   PyCharm
"""

import allure
from neox_test_common import logger
from neox_test_scenarios.yakushi.pages import NSIPSPage


@allure.epic("薬師丸賢太")
@allure.suite("NSIPS突合")
@allure.sub_suite("NSIPS突合比对路径下的源数据清除验证")
@allure.feature("NSIPS突合")
@allure.story("NSIPS突合比对路径下的源数据清除验证")
@allure.title("测试用例：NSIPS突合比对路径下的源数据清除验证")
def test_nsips_comparison_src_cleanup(config):
    """
    TestCase: NSIPS突合比对路径下的源数据清除验证

    测试NSIPS突合比对路径下的源数据清除功能
    """
    nsips_page = NSIPSPage(config)

    with allure.step("NSIPS突合比对路径下的源数据清除验证"):
        logger.info("< Test :: NSIPS突合比对路径下的源数据清除验证 >")

        # 验证NSIPS页面已加载
        assert nsips_page.verify_nsips_page_loaded(), "NSIPS突合页面未正确加载"

        # 执行源数据清除操作
        assert nsips_page.perform_source_cleanup(), "NSIPS源数据清除操作失败"

        logger.info("NSIPS突合源数据清除验证测试完成")
