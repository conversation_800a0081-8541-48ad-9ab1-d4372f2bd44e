# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-07
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   test_nsips_comparison_detail.py
@Software   :   PyCharm
"""

import allure
from neox_test_common import logger
from neox_test_scenarios.yakushi.pages import NSIPSPage


@allure.epic("薬師丸賢太")
@allure.suite("NSIPS突合")
@allure.sub_suite("NSIPS调剂突合详细（弹窗）")
@allure.feature("NSIPS突合")
@allure.story("NSIPS调剂突合详细（弹窗）")
@allure.title("测试用例：NSIPS调剂突合详细（弹窗）")
def test_nsips_comparison_detail_displayed_by_popup_window(config):
    """
    TestCase: NSIPS调剂突合详细（弹窗）

    测试通过弹窗显示NSIPS调剂突合详细信息
    """
    nsips_page = NSIPSPage(config)

    with allure.step("NSIPS调剂突合详细（弹窗）"):
        logger.info("< Test :: NSIPS调剂突合详细（弹窗） >")

        # 验证NSIPS页面已加载
        assert nsips_page.verify_nsips_page_loaded(), "NSIPS突合页面未正确加载"

        # 显示处方详细信息（弹窗方式）
        assert nsips_page.display_prescription_detail(0), "显示NSIPS处方详细信息失败"

        logger.info("NSIPS调剂突合详细（弹窗）测试完成")


@allure.epic("薬師丸賢太")
@allure.suite("NSIPS突合")
@allure.sub_suite("NSIPS调剂突合详细（分页）")
@allure.feature("NSIPS突合")
@allure.story("NSIPS调剂突合详细（分页）")
@allure.title("测试用例：NSIPS调剂突合详细（分页）")
def test_nsips_comparison_detail_displayed_by_paging_column(config):
    """
    TestCase: NSIPS调剂突合详细（分页）

    测试通过分页方式显示NSIPS调剂突合详细信息
    """
    nsips_page = NSIPSPage(config)

    with allure.step("NSIPS调剂突合详细（分页）"):
        logger.info("< Test :: NSIPS调剂突合详细（分页） >")

        # 验证NSIPS页面已加载
        assert nsips_page.verify_nsips_page_loaded(), "NSIPS突合页面未正确加载"

        # 通过分页方式显示详细信息
        assert nsips_page.display_detail_by_paging(), "通过分页显示NSIPS详细信息失败"

        logger.info("NSIPS调剂突合详细（分页）测试完成")


@allure.epic("薬師丸賢太")
@allure.suite("NSIPS突合")
@allure.sub_suite("NSIPS突合跳转处方笺履历详细")
@allure.feature("NSIPS突合")
@allure.story("NSIPS突合跳转处方笺履历详细")
@allure.title("测试用例：NSIPS突合跳转处方笺履历详细")
def test_nsips_comparison_dispaly_prescription_detail(config):
    """
    TestCase: NSIPS突合跳转处方笺履历详细

    测试从NSIPS突合页面跳转到处方笺履历详细页面
    """
    nsips_page = NSIPSPage(config)

    with allure.step("NSIPS突合跳转处方笺履历详细"):
        logger.info("< Test :: NSIPS突合跳转处方笺履历详细 >")

        # 验证NSIPS页面已加载
        assert nsips_page.verify_nsips_page_loaded(), "NSIPS突合页面未正确加载"

        # 跳转到处方笺履历详细页面
        assert nsips_page.navigate_to_prescription_detail(), (
            "跳转到处方笺履历详细页面失败"
        )

        logger.info("NSIPS突合跳转处方笺履历详细测试完成")
