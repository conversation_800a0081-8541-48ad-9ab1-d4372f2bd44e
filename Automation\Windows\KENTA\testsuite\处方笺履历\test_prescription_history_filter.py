# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-07
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   test_prescription_history_filter.py
@Software   :   PyCharm
"""

import allure
from neox_test_common import logger
from neox_test_scenarios.yakushi.pages import PrescriptionHistoryPage


@allure.epic("薬師丸賢太")
@allure.suite("处方笺履历")
@allure.sub_suite("筛选项自动补充")
@allure.feature("处方笺履历")
@allure.story("筛选项自动补充")
@allure.title("测试用例：筛选项自动补充")
def test_prescription_history_valid_filter_items_if_auto_filled(config):
    """
    TestCase: 处方笺履历 - 筛选项自动补充

    测试处方笺履历筛选项的自动补充功能
    """
    prescription_page = PrescriptionHistoryPage(config)

    with allure.step("筛选项自动补充"):
        logger.info("< Test :: 筛选项自动补充 >")

        # 验证处方笺履历页面已加载
        assert prescription_page.verify_prescription_history_page_loaded(), (
            "处方笺履历页面未正确加载"
        )

        # 验证筛选项自动补充功能
        assert prescription_page.validate_filter_auto_completion(), (
            "筛选项自动补充功能验证失败"
        )

        logger.info("筛选项自动补充测试完成")


@allure.epic("薬師丸賢太")
@allure.suite("处方笺履历")
@allure.sub_suite("筛选过滤条件（下拉框）")
@allure.feature("处方笺履历")
@allure.story("筛选过滤条件（下拉框）")
@allure.title("测试用例：筛选过滤条件（下拉框）")
def test_prescription_history_filter_select_items(config):
    """
    TestCase: 处方笺履历 - 筛选过滤条件（下拉框）

    测试使用下拉框进行筛选过滤的功能
    """
    prescription_page = PrescriptionHistoryPage(config)

    with allure.step("筛选过滤条件（下拉框）"):
        logger.info("< Test :: 筛选过滤条件（下拉框） >")

        # 验证处方笺履历页面已加载
        assert prescription_page.verify_prescription_history_page_loaded(), (
            "处方笺履历页面未正确加载"
        )

        # 使用下拉框筛选条件进行过滤
        filter_conditions = {"status": "已处理", "type": "普通处方"}
        assert prescription_page.filter_by_select_items(filter_conditions), (
            "下拉框筛选功能失败"
        )

        logger.info("下拉框筛选测试完成")


@allure.epic("薬師丸賢太")
@allure.suite("处方笺履历")
@allure.sub_suite("筛选过滤条件（文本框）")
@allure.feature("处方笺履历")
@allure.story("筛选过滤条件（文本框）")
@allure.title("测试用例：筛选过滤条件（文本框）")
def test_prescription_history_filter_text_items(config):
    """
    TestCase: 处方笺履历 - 筛选过滤条件（文本框）

    测试使用文本框进行筛选过滤的功能
    """
    prescription_page = PrescriptionHistoryPage(config)

    with allure.step("筛选过滤条件（文本框）"):
        logger.info("< Test :: 筛选过滤条件（文本框） >")

        # 验证处方笺履历页面已加载
        assert prescription_page.verify_prescription_history_page_loaded(), (
            "处方笺履历页面未正确加载"
        )

        # 使用文本框筛选条件进行过滤
        filter_conditions = {"patient_name": "测试患者", "prescription_id": "RX001"}
        assert prescription_page.filter_by_text_items(filter_conditions), (
            "文本框筛选功能失败"
        )

        logger.info("文本框筛选测试完成")


@allure.epic("薬師丸賢太")
@allure.suite("处方笺履历")
@allure.sub_suite("筛选过滤条件（不具合-有不具合报告）")
@allure.feature("处方笺履历")
@allure.story("筛选过滤条件（不具合-有不具合报告）")
@allure.title("测试用例：筛选过滤条件（不具合-有不具合报告）")
def test_prescription_history_filter_fault_report_exists(config):
    """
    TestCase: 处方笺履历 - 筛选过滤条件（不具合-有不具合报告）

    测试筛选有故障报告的处方笺记录
    """
    prescription_page = PrescriptionHistoryPage(config)

    with allure.step("筛选过滤条件（不具合-有不具合报告）"):
        logger.info("< Test :: 筛选过滤条件（不具合-有不具合报告） >")

        # 验证处方笺履历页面已加载
        assert prescription_page.verify_prescription_history_page_loaded(), (
            "处方笺履历页面未正确加载"
        )

        # 筛选有故障报告的记录
        assert prescription_page.filter_by_fault_report_status(has_fault_report=True), (
            "筛选有故障报告的记录失败"
        )

        logger.info("有故障报告筛选测试完成")


@allure.epic("薬師丸賢太")
@allure.suite("处方笺履历")
@allure.sub_suite("筛选过滤条件（不具合-无不具合报告）")
@allure.feature("处方笺履历")
@allure.story("筛选过滤条件（不具合-无不具合报告）")
@allure.title("测试用例：筛选过滤条件（不具合-无不具合报告）")
def test_prescription_history_filter_fault_report_not_exists(config):
    """
    TestCase: 处方笺履历 - 筛选过滤条件（不具合-无不具合报告）

    测试筛选无故障报告的处方笺记录
    """
    prescription_page = PrescriptionHistoryPage(config)

    with allure.step("筛选过滤条件（不具合-无不具合报告）"):
        logger.info("< Test :: 筛选过滤条件（不具合-无不具合报告） >")

        # 验证处方笺履历页面已加载
        assert prescription_page.verify_prescription_history_page_loaded(), (
            "处方笺履历页面未正确加载"
        )

        # 筛选无故障报告的记录
        assert prescription_page.filter_by_fault_report_status(
            has_fault_report=False
        ), "筛选无故障报告的记录失败"

        logger.info("无故障报告筛选测试完成")
