# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-07
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   test_prescription_history_detail.py
@Software   :   PyCharm
"""

import allure
from neox_test_common import logger
from neox_test_scenarios.yakushi.pages import PrescriptionHistoryPage


@allure.epic("薬師丸賢太")
@allure.suite("处方笺履历")
@allure.sub_suite("展示处方笺履历详细")
@allure.feature("处方笺履历")
@allure.story("展示处方笺履历详细")
@allure.title("测试用例：展示处方笺履历详细")
def test_prescription_history_display_detail(config):
    """
    TestCase: 处方笺履历 - 展示处方笺履历详细

    测试处方笺履历详细信息的展示功能
    """
    prescription_page = PrescriptionHistoryPage(config)

    with allure.step("展示处方笺履历详细"):
        logger.info("< Test :: 展示处方笺履历详细 >")

        # 验证处方笺履历页面已加载
        assert prescription_page.verify_prescription_history_page_loaded(), (
            "处方笺履历页面未正确加载"
        )

        # 显示第一个处方笺的详细信息
        assert prescription_page.display_prescription_detail(0), (
            "显示处方笺详细信息失败"
        )

        logger.info("处方笺履历详细展示测试完成")
