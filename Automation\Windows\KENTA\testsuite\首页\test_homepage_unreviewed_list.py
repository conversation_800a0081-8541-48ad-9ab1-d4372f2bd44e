# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-07
<AUTHOR>   <PERSON><PERSON><PERSON>u
@Email      :   <EMAIL>
@File       :   test_homepage_unreviewed_list.py
@Software   :   PyCharm
"""

import allure
from neox_test_common import logger
from neox_test_scenarios.yakushi.pages import HomePage


@allure.epic("薬師丸賢太")
@allure.suite("首页")
@allure.sub_suite("未阅览列表展示（QR->QR）")
@allure.feature("首页")
@allure.story("未阅览列表展示（QR->QR）")
@allure.title("测试用例：未阅览列表展示（QR->QR）")
def test_homepage_unreviewed_list_qr2qr(config):
    """
    TestCase: 首页 - 未阅览列表展示（QR->QR）

    测试从QR模式切换到QR模式时未阅览列表的展示功能
    """
    homepage = HomePage(config)

    with allure.step("验证未阅览列表展示（QR->QR）"):
        logger.info("< Test :: 未阅览列表展示（QR->QR） >")

        # 验证首页已加载
        assert homepage.verify_homepage_loaded(), "首页未正确加载"

        # 执行QR到QR模式的未阅览列表展示验证
        assert homepage.verify_unreviewed_list_display("QR", "QR"), (
            "QR->QR模式下未阅览列表展示验证失败"
        )

        logger.info("未阅览列表展示（QR->QR）测试完成")


@allure.epic("薬師丸賢太")
@allure.suite("首页")
@allure.sub_suite("未阅览列表展示（OCR->QR）")
@allure.feature("首页")
@allure.story("未阅览列表展示（OCR->QR）")
@allure.title("测试用例：未阅览列表展示（OCR->QR）")
def test_homepage_unreviewed_list_ocr2qr(config):
    """
    TestCase: 首页 - 未阅览列表展示（OCR->QR）

    测试从OCR模式切换到QR模式时未阅览列表的展示功能
    """
    homepage = HomePage(config)

    with allure.step("验证未阅览列表展示（OCR->QR）"):
        logger.info("< Test :: 未阅览列表展示（OCR->QR） >")

        # 验证首页已加载
        assert homepage.verify_homepage_loaded(), "首页未正确加载"

        # 执行OCR到QR模式的未阅览列表展示验证
        assert homepage.verify_unreviewed_list_display("OCR", "QR"), (
            "OCR->QR模式下未阅览列表展示验证失败"
        )

        logger.info("未阅览列表展示（OCR->QR）测试完成")


@allure.epic("薬師丸賢太")
@allure.suite("首页")
@allure.sub_suite("未阅览列表展示（QR->OCR）")
@allure.feature("首页")
@allure.story("未阅览列表展示（QR->OCR）")
@allure.title("测试用例：未阅览列表展示（QR->OCR）")
def test_homepage_unreviewed_list_qr2ocr(config):
    """
    TestCase: 首页 - 未阅览列表展示（QR->OCR）

    测试从QR模式切换到OCR模式时未阅览列表的展示功能
    """
    homepage = HomePage(config)

    with allure.step("验证未阅览列表展示（QR->OCR）"):
        logger.info("< Test :: 未阅览列表展示（QR->OCR） >")

        # 验证首页已加载
        assert homepage.verify_homepage_loaded(), "首页未正确加载"

        # 执行QR到OCR模式的未阅览列表展示验证
        assert homepage.verify_unreviewed_list_display("QR", "OCR"), (
            "QR->OCR模式下未阅览列表展示验证失败"
        )

        logger.info("未阅览列表展示（QR->OCR）测试完成")


@allure.epic("薬師丸賢太")
@allure.suite("首页")
@allure.sub_suite("未阅览列表展示（OCR->OCR）")
@allure.feature("首页")
@allure.story("未阅览列表展示（OCR->OCR）")
@allure.title("测试用例：未阅览列表展示（OCR->OCR）")
def test_homepage_unreviewed_list_ocr2ocr(config):
    """
    TestCase: 首页 - 未阅览列表展示（OCR->OCR）

    测试从OCR模式切换到OCR模式时未阅览列表的展示功能
    """
    homepage = HomePage(config)

    with allure.step("验证未阅览列表展示（OCR->OCR）"):
        logger.info("< Test :: 未阅览列表展示（OCR->OCR） >")

        # 验证首页已加载
        assert homepage.verify_homepage_loaded(), "首页未正确加载"

        # 执行OCR到OCR模式的未阅览列表展示验证
        assert homepage.verify_unreviewed_list_display("OCR", "OCR"), (
            "OCR->OCR模式下未阅览列表展示验证失败"
        )

        logger.info("未阅览列表展示（OCR->OCR）测试完成")
