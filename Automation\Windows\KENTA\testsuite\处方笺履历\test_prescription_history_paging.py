# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-07
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   test_prescription_history_paging.py
@Software   :   PyCharm
"""

import allure
from neox_test_common import logger
from neox_test_scenarios.yakushi.pages import PrescriptionHistoryPage


@allure.epic("薬師丸賢太")
@allure.suite("处方笺履历")
@allure.sub_suite("切换处方笺履历列表分页")
@allure.feature("处方笺履历")
@allure.story("切换处方笺履历列表分页")
@allure.title("测试用例：切换处方笺履历列表分页")
def test_prescription_history_toggle_paging_column(config):
    """
    TestCase: 处方笺履历 - 切换处方笺履历列表分页

    测试处方笺履历列表的分页切换功能
    """
    prescription_page = PrescriptionHistoryPage(config)

    with allure.step("切换处方笺履历列表分页"):
        logger.info("< Test :: 切换处方笺履历列表分页 >")

        # 验证处方笺履历页面已加载
        assert prescription_page.verify_prescription_history_page_loaded(), (
            "处方笺履历页面未正确加载"
        )

        # 切换分页列显示
        assert prescription_page.toggle_paging_column(), "切换分页列显示失败"

        logger.info("处方笺履历列表分页切换测试完成")
